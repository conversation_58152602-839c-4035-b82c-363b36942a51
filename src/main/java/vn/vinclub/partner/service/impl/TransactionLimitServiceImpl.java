package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.UserQuotaDto;
import vn.vinclub.partner.domain.dto.module.config.PointLimitConfig;
import vn.vinclub.partner.domain.dto.module.config.SpendPointConfig;
import vn.vinclub.partner.domain.dto.module.config.TopUpPointConfig;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.service.PartnerModuleService;
import vn.vinclub.partner.service.PartnerPointConfigService;
import vn.vinclub.partner.service.PartnerPointTransactionHistoryService;
import vn.vinclub.partner.service.TransactionLimitService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TransactionLimitServiceImpl implements TransactionLimitService {

    private static final String DAILY_USER_QUOTA_CACHE_KEY = "partner_svc_daily_user_quota:%s:%s";
    private static final String MONTHLY_USER_QUOTA_CACHE_KEY = "partner_svc_monthly_user_quota:%s:%s";

    private static final Set<PointHistoryStatus> COUNTED_STATUSES = Set.of(PointHistoryStatus.PROCESSING, PointHistoryStatus.SUCCESS);

    private final PartnerModuleService partnerModuleService;
    private final PartnerPointConfigService partnerPointConfigService;
    private final PartnerPointTransactionHistoryService partnerPointTransactionHistoryService;

    private final RedissonClient redissonClient;
    private final BaseJsonUtils jsonUtils;
    private final JsonJacksonCodec jsonJacksonCodec;

    @Value("${default.vinclub.point.code}")
    private String defaultVinclubPointCode;

    @Value("${default.vinclub.point.cash.value}")
    private BigDecimal defaultVinclubPointCashValue;

    @Value("${partner.transaction.max-request-by-user-per-day}")
    private Long maxRequestByUserPerDay;

    @Value("${partner.transaction.max-cash-amount-by-user-per-day}")
    private Long maxCashAmountByUserPerDay;

    @Profiler
    @Override
    public void validateLimits(Long partnerId, String partnerUserId, Long pointAmount, String pointCode, TransactionType transactionType, ActorSystem actorSystem) {
        UserQuotaDto userQuota = getDailyUserQuota(partnerId, partnerUserId);
        log.debug("userQuota: {}", jsonUtils.toString(userQuota));

        if (userQuota.getTotalTransaction() >= maxRequestByUserPerDay) {
            throw new BusinessLogicException(AppErrorCode.DAILY_TRANSACTION_COUNT_LIMIT_EXCEEDED);
        }

        BigDecimal cashValue = calculateCashValue(pointAmount, pointCode, partnerId);
        if (userQuota.getTotalCashAmount().add(cashValue).compareTo(BigDecimal.valueOf(maxCashAmountByUserPerDay)) > 0) {
            throw new BusinessLogicException(AppErrorCode.DAILY_CASH_AMOUNT_LIMIT_EXCEEDED);
        }

        // validate by partner config
        validateByPartnerModuleConfig(partnerId, partnerUserId, pointAmount, pointCode, transactionType, actorSystem);
    }

    @Profiler
    @Override
    public void updateQuota(PartnerPointTransactionHistory oldTransaction, PartnerPointTransactionHistory newTransaction) {
        try {
            // Handle transaction creation
            if (oldTransaction == null && newTransaction != null) {
                if (COUNTED_STATUSES.contains(newTransaction.getStatus())) {
                    incrementQuota(newTransaction);
                }
                return;
            }

            // Handle transaction deletion
            if (oldTransaction != null && newTransaction == null) {
                if (COUNTED_STATUSES.contains(oldTransaction.getStatus())) {
                    decrementQuota(oldTransaction);
                }
                return;
            }

            // Handle transaction update
            if (newTransaction != null) {
                boolean oldCounted = COUNTED_STATUSES.contains(oldTransaction.getStatus());
                boolean newCounted = COUNTED_STATUSES.contains(newTransaction.getStatus());

                if (!oldCounted && newCounted) {
                    // Transaction moved to counted status
                    incrementQuota(newTransaction);
                } else if (oldCounted && !newCounted) {
                    // Transaction moved from counted status
                    decrementQuota(oldTransaction);
                }
            }
        } catch (Exception e) {
            log.warn("Error while updating quota: {} - invalidate quota cache", e.getMessage());
            var partnerId = Objects.nonNull(oldTransaction) ? oldTransaction.getPartnerId() : newTransaction.getPartnerId();
            var partnerUserId = Objects.nonNull(oldTransaction) ? oldTransaction.getPartnerUserId() : newTransaction.getPartnerUserId();
            invalidateUserQuotaCache(partnerId, partnerUserId);
        }
    }

    @Profiler
    private void invalidateUserQuotaCache(Long partnerId, String partnerUserId) {
        log.debug("Invalidating user quota cache for partnerId={}, partnerUserId={}", partnerId, partnerUserId);
        redissonClient.getBucket(String.format(DAILY_USER_QUOTA_CACHE_KEY, partnerId, partnerUserId), jsonJacksonCodec).delete();
        redissonClient.getBucket(String.format(MONTHLY_USER_QUOTA_CACHE_KEY, partnerId, partnerUserId), jsonJacksonCodec).delete();
    }

    @Profiler
    private void validateByPartnerModuleConfig(Long partnerId, String partnerUserId, Long pointAmount, String pointCode, TransactionType transactionType, ActorSystem actorSystem) {
        PointLimitConfig config = getPointLimitConfig(partnerId, transactionType);
        if (config == null) {
            return;
        }

        if (ActorSystem.VINCLUB.equals(actorSystem)) { // Vinclub -> Partner
            validatePartnerPointLimit(config, partnerId, partnerUserId, pointAmount, pointCode, transactionType);
        } else { // Partner -> Vinclub
            validateVinclubPointLimit(config, partnerId, partnerUserId, pointAmount, transactionType);
        }
    }

    @Profiler
    private void validateVinclubPointLimit(PointLimitConfig config, Long partnerId, String partnerUserId, Long pointAmount, TransactionType transactionType) {
        if (!Objects.isNull(config.getMaxVclubPointPerTransaction())) {
            if (pointAmount > config.getMaxVclubPointPerTransaction()) {
                var errorData = Map.of(
                        "maxPointPerTransaction", config.getMaxVclubPointPerTransaction()
                );
                throw new BusinessLogicException(errorData, AppErrorCode.POINT_PER_TRANSACTION_LIMIT_EXCEEDED);
            }
        }

        if (!Objects.isNull(config.getVclubDailyQuota())) {
            UserQuotaDto userQuota = getDailyUserQuota(partnerId, partnerUserId);
            Long totalVinclubPointAmountByType = userQuota.getTotalVinclubPointAmountByType().getOrDefault(transactionType, 0L);
            if (totalVinclubPointAmountByType + pointAmount > config.getVclubDailyQuota()) {
                var errorData = Map.of(
                        "dailyQuota", config.getVclubDailyQuota(),
                        "remainingDailyQuota", config.getVclubDailyQuota() - totalVinclubPointAmountByType
                );
                throw new BusinessLogicException(errorData, AppErrorCode.DAILY_POINT_QUOTA_EXCEEDED);
            }
        }

        if (!Objects.isNull(config.getVclubMonthlyQuota())) {
            UserQuotaDto userQuota = getMonthlyUserQuota(partnerId, partnerUserId);
            Long totalVinclubPointAmountByType = userQuota.getTotalVinclubPointAmountByType().getOrDefault(transactionType, 0L);
            if (totalVinclubPointAmountByType + pointAmount > config.getVclubMonthlyQuota()) {
                var errorData = Map.of(
                        "monthlyQuota", config.getVclubMonthlyQuota(),
                        "remainingMonthlyQuota", config.getVclubMonthlyQuota() - totalVinclubPointAmountByType
                );
                throw new BusinessLogicException(errorData, AppErrorCode.MONTHLY_POINT_QUOTA_EXCEEDED);
            }
        }
    }

    @Profiler
    private void validatePartnerPointLimit(PointLimitConfig config, Long partnerId, String partnerUserId, Long pointAmount, String pointCode, TransactionType transactionType) {
        if (MapUtils.isNotEmpty(config.getMaxPartnerPointPerTransactionByPointCode())) {
            Long maxPointPerTransaction = config.getMaxPartnerPointPerTransactionByPointCode().get(pointCode);
            if (maxPointPerTransaction != null && pointAmount > maxPointPerTransaction) {
                var errorData = Map.of(
                        "maxPointPerTransaction", maxPointPerTransaction
                );
                throw new BusinessLogicException(errorData, AppErrorCode.POINT_PER_TRANSACTION_LIMIT_EXCEEDED);
            }
        }

        if (MapUtils.isNotEmpty(config.getPartnerDailyQuotaByPointCode())) {
            Long dailyQuota = config.getPartnerDailyQuotaByPointCode().get(pointCode);
            if (dailyQuota != null) {
                UserQuotaDto userQuota = getDailyUserQuota(partnerId, partnerUserId);
                Long totalPartnerPointAmountByTypeAndPointCode = userQuota.getTotalPartnerPointAmountByTypeAndPointCode().getOrDefault(transactionType, Map.of()).getOrDefault(pointCode, 0L);
                if (totalPartnerPointAmountByTypeAndPointCode + pointAmount > dailyQuota) {
                    var errorData = Map.of(
                            "dailyQuota", dailyQuota,
                            "remainingDailyQuota", dailyQuota - totalPartnerPointAmountByTypeAndPointCode
                    );
                    throw new BusinessLogicException(errorData, AppErrorCode.DAILY_POINT_QUOTA_EXCEEDED);
                }
            }
        }

        if (MapUtils.isNotEmpty(config.getPartnerMonthlyQuotaByPointCode())) {
            Long monthlyQuota = config.getPartnerMonthlyQuotaByPointCode().get(pointCode);
            if (monthlyQuota != null) {
                UserQuotaDto userQuota = getMonthlyUserQuota(partnerId, partnerUserId);
                Long totalPartnerPointAmountByTypeAndPointCode = userQuota.getTotalPartnerPointAmountByTypeAndPointCode().getOrDefault(transactionType, Map.of()).getOrDefault(pointCode, 0L);
                if (totalPartnerPointAmountByTypeAndPointCode + pointAmount > monthlyQuota) {
                    var errorData = Map.of(
                            "monthlyQuota", monthlyQuota,
                            "remainingMonthlyQuota", monthlyQuota - totalPartnerPointAmountByTypeAndPointCode
                    );
                    throw new BusinessLogicException(errorData, AppErrorCode.MONTHLY_POINT_QUOTA_EXCEEDED);
                }
            }
        }
    }

    @Profiler
    private PointLimitConfig getPointLimitConfig(Long partnerId, TransactionType transactionType) {
        return switch (transactionType) {
            case TOP_UP_POINT -> partnerModuleService.optByPartnerAndModule(partnerId, ModuleType.TOPUP_POINT)
                    .map(module -> jsonUtils.treeToValue(module.getConfig(), TopUpPointConfig.class))
                    .orElse(null);
            case SPEND_POINT -> partnerModuleService.optByPartnerAndModule(partnerId, ModuleType.SPEND_POINT)
                    .map(module -> jsonUtils.treeToValue(module.getConfig(), SpendPointConfig.class))
                    .orElse(null);
            default -> null;
        };
    }

    @Profiler
    private UserQuotaDto getDailyUserQuota(Long partnerId, String partnerUserId) {
        String cacheKey = String.format(DAILY_USER_QUOTA_CACHE_KEY, partnerId, partnerUserId);
        RBucket<UserQuotaDto> bucket = redissonClient.getBucket(cacheKey, jsonJacksonCodec);

        if (bucket.isExists()) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "getDailyUserQuota.cache.hit")) {
                return bucket.get();
            }
        }

        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "getDailyUserQuota.cache.miss")) {
            Long startOfDay = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            Long endOfDay = LocalDate.now().atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

            UserQuotaDto quota = getUserQuotaDto(partnerId, partnerUserId, startOfDay, endOfDay);
            bucket.set(quota, Duration.ofMillis(getRemainingTimeToEndOfDay()));

            return quota;
        }
    }

    @Profiler
    private UserQuotaDto getMonthlyUserQuota(Long partnerId, String partnerUserId) {
        String cacheKey = String.format(MONTHLY_USER_QUOTA_CACHE_KEY, partnerId, partnerUserId);
        RBucket<UserQuotaDto> bucket = redissonClient.getBucket(cacheKey, jsonJacksonCodec);

        if (bucket.isExists()) {
            return bucket.get();
        }

        Long startOfMonth = LocalDate.now().withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth()).atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        UserQuotaDto quota = getUserQuotaDto(partnerId, partnerUserId, startOfMonth, endOfMonth);
        bucket.set(quota, Duration.ofMillis(getRemainingTimeToEndOfDay()));

        return quota;
    }

    @Profiler
    private UserQuotaDto getUserQuotaDto(Long partnerId, String partnerUserId, Long fromTime, Long endTime) {
        Page<PartnerPointTransactionHistory> transactions = partnerPointTransactionHistoryService.filter(
                PartnerPointTxnFilterDto.builder()
                        .partnerId(partnerId)
                        .partnerUserId(partnerUserId)
                        .requestTimeFrom(fromTime)
                        .requestTimeTo(endTime)
                        .build(),
                Pageable.unpaged()
        );
        return calculateUserQuota(transactions.getContent());
    }

    @Profiler
    private UserQuotaDto calculateUserQuota(List<PartnerPointTransactionHistory> transactions) {
        if (transactions.isEmpty()) {
            return UserQuotaDto.builder()
                    .totalTransaction(0L)
                    .totalCashAmount(BigDecimal.ZERO)
                    .build();
        }

        Map<TransactionType, Map<String, Long>> totalPartnerPointTransactionByTypeAndPointCode = transactions.stream()
                .filter(transaction -> ActorSystem.VINCLUB.equals(transaction.getActorSystem())) // Vinclub -> Partner
                .collect(Collectors.groupingBy(PartnerPointTransactionHistory::getTransactionType,
                        Collectors.groupingBy(PartnerPointTransactionHistory::getPointCode, Collectors.counting())));

        Map<TransactionType, Map<String, Long>> totalPartnerPointAmountByTypeAndPointCode = transactions.stream()
                .filter(transaction -> ActorSystem.VINCLUB.equals(transaction.getActorSystem())) // Vinclub -> Partner
                .collect(Collectors.groupingBy(PartnerPointTransactionHistory::getTransactionType,
                        Collectors.groupingBy(PartnerPointTransactionHistory::getPointCode,
                                Collectors.reducing(0L, PartnerPointTransactionHistory::getPointAmount, Long::sum))));

        Map<TransactionType, Long> totalVinclubPointTransactionByType = transactions.stream()
                .filter(transaction -> ActorSystem.PARTNER.equals(transaction.getActorSystem())) // Partner -> Vinclub
                .collect(Collectors.groupingBy(PartnerPointTransactionHistory::getTransactionType, Collectors.counting()));

        Map<TransactionType, Long> totalVinclubPointAmountByType = transactions.stream()
                .filter(transaction -> ActorSystem.PARTNER.equals(transaction.getActorSystem())) // Partner -> Vinclub
                .collect(Collectors.groupingBy(PartnerPointTransactionHistory::getTransactionType,
                        Collectors.reducing(0L, PartnerPointTransactionHistory::getPointAmount, Long::sum)));


        BigDecimal totalCashAmount = transactions.stream()
                .map(transaction -> calculateCashValueForTransaction(transaction, transaction.getPartnerId()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return UserQuotaDto.builder()
                .totalTransaction((long) transactions.size())
                .totalCashAmount(totalCashAmount)
                .totalPartnerPointTransactionByTypeAndPointCode(totalPartnerPointTransactionByTypeAndPointCode)
                .totalPartnerPointAmountByTypeAndPointCode(totalPartnerPointAmountByTypeAndPointCode)
                .totalVinclubPointTransactionByType(totalVinclubPointTransactionByType)
                .totalVinclubPointAmountByType(totalVinclubPointAmountByType)
                .build();
    }

    @Profiler
    private void decrementQuota(PartnerPointTransactionHistory transaction) {
        if (transaction == null) {
            log.warn("Cannot decrement quota: transaction is null");
            return;
        }

        if (transaction.getPartnerId() == null || transaction.getPartnerUserId() == null) {
            log.warn("Cannot decrement quota: partnerId or partnerUserId is null for transaction {}",
                    transaction.getTransactionId());
            return;
        }

        try {
            log.debug("Decrementing quota for partnerId={}, partnerUserId={}, transactionId={}, pointAmount={}, pointCode={}",
                    transaction.getPartnerId(), transaction.getPartnerUserId(), transaction.getTransactionId(),
                    transaction.getPointAmount(), transaction.getPointCode());

            // Update daily quota
            updateQuotaCache(transaction, false, true);

            // Update monthly quota
            updateQuotaCache(transaction, false, false);

            log.debug("Successfully decremented quota for transaction {}", transaction.getTransactionId());
        } catch (Exception e) {
            log.error("Failed to decrement quota for transaction {}: {}",
                    transaction.getTransactionId(), e.getMessage(), e);
            throw new RuntimeException("Failed to decrement quota", e);
        }
    }

    @Profiler
    private void incrementQuota(PartnerPointTransactionHistory transaction) {
        if (transaction == null) {
            log.warn("Cannot increment quota: transaction is null");
            return;
        }

        if (transaction.getPartnerId() == null || transaction.getPartnerUserId() == null) {
            log.warn("Cannot increment quota: partnerId or partnerUserId is null for transaction {}",
                    transaction.getTransactionId());
            return;
        }

        try {
            log.debug("Incrementing quota for partnerId={}, partnerUserId={}, transactionId={}, pointAmount={}, pointCode={}",
                    transaction.getPartnerId(), transaction.getPartnerUserId(), transaction.getTransactionId(),
                    transaction.getPointAmount(), transaction.getPointCode());

            // Update daily quota
            updateQuotaCache(transaction, true, true);

            // Update monthly quota
            updateQuotaCache(transaction, true, false);

            log.debug("Successfully incremented quota for transaction {}", transaction.getTransactionId());
        } catch (Exception e) {
            log.error("Failed to increment quota for transaction {}: {}",
                    transaction.getTransactionId(), e.getMessage(), e);
            throw new RuntimeException("Failed to increment quota", e);
        }
    }

    @Profiler
    private void updateQuotaCache(PartnerPointTransactionHistory transaction, boolean isIncrement, boolean isDaily) {
        String cacheKey = isDaily
                ? String.format(DAILY_USER_QUOTA_CACHE_KEY, transaction.getPartnerId(), transaction.getPartnerUserId())
                : String.format(MONTHLY_USER_QUOTA_CACHE_KEY, transaction.getPartnerId(), transaction.getPartnerUserId());

        RBucket<UserQuotaDto> bucket = redissonClient.getBucket(cacheKey, jsonJacksonCodec);

        // Use Redis atomic operations for thread-safety
        UserQuotaDto currentQuota;
        if (bucket.isExists()) {
            currentQuota = bucket.get();
        } else {
            // If cache doesn't exist, load from database
            currentQuota = isDaily
                    ? getDailyUserQuota(transaction.getPartnerId(), transaction.getPartnerUserId())
                    : getMonthlyUserQuota(transaction.getPartnerId(), transaction.getPartnerUserId());
        }

        if (currentQuota == null) {
            log.debug("Current quota is null for partnerId={}, partnerUserId={}, creating empty quota",
                    transaction.getPartnerId(), transaction.getPartnerUserId());
            currentQuota = createEmptyQuota();
        }

        // Calculate the updated quota
        UserQuotaDto updatedQuota = calculateUpdatedQuota(currentQuota, transaction, isIncrement);

        // Validate the updated quota (prevent negative values for decrement)
        if (!isIncrement && !isValidQuotaForDecrement(updatedQuota)) {
            log.error("Decrement operation would result in negative quota values for transaction {}",
                    transaction.getTransactionId());
            throw new IllegalStateException("Cannot decrement quota: would result in negative values");
        }

        // Update cache with appropriate TTL
        Duration ttl = Duration.ofMillis(getRemainingTimeToEndOfDay());
        bucket.set(updatedQuota, ttl);

        log.debug("Updated {} quota cache for partnerId={}, partnerUserId={}, operation={}",
                isDaily ? "daily" : "monthly", transaction.getPartnerId(), transaction.getPartnerUserId(),
                isIncrement ? "increment" : "decrement");
    }

    @Profiler
    private BigDecimal calculateCashValueForTransaction(PartnerPointTransactionHistory transaction, Long partnerId) {
        Long pointAmount = transaction.getPointAmount();
        String pointCode = transaction.getPointCode();

        if (pointAmount == null || pointAmount <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal cashValuePerPoint = getCashValuePerPoint(pointCode, partnerId);
        return cashValuePerPoint.multiply(BigDecimal.valueOf(pointAmount));
    }

    @Profiler
    private BigDecimal calculateCashValue(Long pointAmount, String pointCode, Long partnerId) {
        if (pointAmount == null || pointAmount <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal cashValuePerPoint = getCashValuePerPoint(pointCode, partnerId);
        return cashValuePerPoint.multiply(BigDecimal.valueOf(pointAmount));
    }

    @Profiler
    private BigDecimal getCashValuePerPoint(String pointCode, Long partnerId) {
        // Default to VinClub point cash value
        if (defaultVinclubPointCode.equals(pointCode)) {
            return defaultVinclubPointCashValue;
        }

        // For other point codes, get from partner point config
        try {
            var pointConfig = partnerPointConfigService.findByCode(partnerId, pointCode);
            if (pointConfig.getExchangeCashRate() != null) {
                BigDecimal exchangeValue = pointConfig.getExchangeCashRate().getExchangeValue();
                Long pointValue = pointConfig.getExchangeCashRate().getPointValue();

                if (exchangeValue != null && pointValue != null && pointValue > 0) {
                    return exchangeValue.divide(BigDecimal.valueOf(pointValue), RoundingMode.HALF_UP);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to get cash value for pointCode={}, partnerId={}, using 0", pointCode, partnerId, e);
        }

        return BigDecimal.ZERO;
    }

    private Long getRemainingTimeToEndOfDay() {
        return LocalDate.now().atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() - System.currentTimeMillis();
    }

    @Profiler
    private UserQuotaDto createEmptyQuota() {
        return UserQuotaDto.builder()
                .totalTransaction(0L)
                .totalCashAmount(BigDecimal.ZERO)
                .totalPartnerPointTransactionByTypeAndPointCode(new HashMap<>())
                .totalPartnerPointAmountByTypeAndPointCode(new HashMap<>())
                .totalVinclubPointTransactionByType(new HashMap<>())
                .totalVinclubPointAmountByType(new HashMap<>())
                .build();
    }

    @Profiler
    private UserQuotaDto calculateUpdatedQuota(UserQuotaDto currentQuota, PartnerPointTransactionHistory transaction, boolean isIncrement) {
        if (currentQuota == null || transaction == null) {
            throw new IllegalArgumentException("Current quota and transaction cannot be null");
        }

        // Calculate cash value for this transaction
        BigDecimal transactionCashValue = calculateCashValueForTransaction(transaction, transaction.getPartnerId());

        // Create mutable copies of the maps
        Map<TransactionType, Map<String, Long>> partnerPointTransactionByTypeAndPointCode =
                new HashMap<>(currentQuota.getTotalPartnerPointTransactionByTypeAndPointCode() != null
                        ? currentQuota.getTotalPartnerPointTransactionByTypeAndPointCode() : new HashMap<>());

        Map<TransactionType, Map<String, Long>> partnerPointAmountByTypeAndPointCode =
                new HashMap<>(currentQuota.getTotalPartnerPointAmountByTypeAndPointCode() != null
                        ? currentQuota.getTotalPartnerPointAmountByTypeAndPointCode() : new HashMap<>());

        Map<TransactionType, Long> vinclubPointTransactionByType =
                new HashMap<>(currentQuota.getTotalVinclubPointTransactionByType() != null
                        ? currentQuota.getTotalVinclubPointTransactionByType() : new HashMap<>());

        Map<TransactionType, Long> vinclubPointAmountByType =
                new HashMap<>(currentQuota.getTotalVinclubPointAmountByType() != null
                        ? currentQuota.getTotalVinclubPointAmountByType() : new HashMap<>());

        // Update transaction count and cash amount
        long newTotalTransaction = isIncrement
                ? currentQuota.getTotalTransaction() + 1
                : Math.max(0, currentQuota.getTotalTransaction() - 1);

        BigDecimal newTotalCashAmount = isIncrement
                ? currentQuota.getTotalCashAmount().add(transactionCashValue)
                : currentQuota.getTotalCashAmount().subtract(transactionCashValue);

        // Ensure cash amount doesn't go negative
        if (newTotalCashAmount.compareTo(BigDecimal.ZERO) < 0) {
            newTotalCashAmount = BigDecimal.ZERO;
        }

        // Update based on actor system
        if (ActorSystem.VINCLUB.equals(transaction.getActorSystem())) {
            // Vinclub -> Partner transaction
            updatePartnerPointMaps(partnerPointTransactionByTypeAndPointCode, partnerPointAmountByTypeAndPointCode,
                    transaction, isIncrement);
        } else if (ActorSystem.PARTNER.equals(transaction.getActorSystem())) {
            // Partner -> Vinclub transaction
            updateVinclubPointMaps(vinclubPointTransactionByType, vinclubPointAmountByType,
                    transaction, isIncrement);
        }

        return UserQuotaDto.builder()
                .totalTransaction(newTotalTransaction)
                .totalCashAmount(newTotalCashAmount)
                .totalPartnerPointTransactionByTypeAndPointCode(partnerPointTransactionByTypeAndPointCode)
                .totalPartnerPointAmountByTypeAndPointCode(partnerPointAmountByTypeAndPointCode)
                .totalVinclubPointTransactionByType(vinclubPointTransactionByType)
                .totalVinclubPointAmountByType(vinclubPointAmountByType)
                .build();
    }

    @Profiler
    private void updatePartnerPointMaps(Map<TransactionType, Map<String, Long>> transactionMap,
                                        Map<TransactionType, Map<String, Long>> amountMap,
                                        PartnerPointTransactionHistory transaction,
                                        boolean isIncrement) {
        TransactionType transactionType = transaction.getTransactionType();
        String pointCode = transaction.getPointCode();
        Long pointAmount = transaction.getPointAmount();

        // Update transaction count
        transactionMap.computeIfAbsent(transactionType, k -> new HashMap<>())
                .merge(pointCode, 1L, (existing, increment) ->
                        isIncrement ? existing + increment : Math.max(0, existing - increment));

        // Update point amount
        amountMap.computeIfAbsent(transactionType, k -> new HashMap<>())
                .merge(pointCode, pointAmount, (existing, amount) ->
                        isIncrement ? existing + amount : Math.max(0, existing - amount));
    }

    @Profiler
    private void updateVinclubPointMaps(Map<TransactionType, Long> transactionMap,
                                        Map<TransactionType, Long> amountMap,
                                        PartnerPointTransactionHistory transaction,
                                        boolean isIncrement) {
        TransactionType transactionType = transaction.getTransactionType();
        Long pointAmount = transaction.getPointAmount();

        // Update transaction count
        transactionMap.merge(transactionType, 1L, (existing, increment) ->
                isIncrement ? existing + increment : Math.max(0, existing - increment));

        // Update point amount
        amountMap.merge(transactionType, pointAmount, (existing, amount) ->
                isIncrement ? existing + amount : Math.max(0, existing - amount));
    }

    @Profiler
    private boolean isValidQuotaForDecrement(UserQuotaDto quota) {
        if (quota == null) {
            return false;
        }

        // Check if total transaction count is non-negative
        if (quota.getTotalTransaction() < 0) {
            return false;
        }

        // Check if total cash amount is non-negative
        if (quota.getTotalCashAmount().compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }

        // Check partner point maps for negative values
        if (quota.getTotalPartnerPointTransactionByTypeAndPointCode() != null) {
            for (Map<String, Long> pointCodeMap : quota.getTotalPartnerPointTransactionByTypeAndPointCode().values()) {
                if (pointCodeMap.values().stream().anyMatch(value -> value < 0)) {
                    return false;
                }
            }
        }

        if (quota.getTotalPartnerPointAmountByTypeAndPointCode() != null) {
            for (Map<String, Long> pointCodeMap : quota.getTotalPartnerPointAmountByTypeAndPointCode().values()) {
                if (pointCodeMap.values().stream().anyMatch(value -> value < 0)) {
                    return false;
                }
            }
        }

        // Check Vinclub point maps for negative values
        if (quota.getTotalVinclubPointTransactionByType() != null) {
            if (quota.getTotalVinclubPointTransactionByType().values().stream().anyMatch(value -> value < 0)) {
                return false;
            }
        }

        if (quota.getTotalVinclubPointAmountByType() != null) {
            return quota.getTotalVinclubPointAmountByType().values().stream().noneMatch(value -> value < 0);
        }

        return true;
    }
}
